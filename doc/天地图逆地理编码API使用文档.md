# 天地图逆地理编码API使用文档

## 概述

天地图逆地理编码API是一个HTTP/HTTPS接口服务，可以将经纬度坐标转换为结构化的地址信息，包括街道、湖泊、江河、POI点等详细地理位置信息。

## 功能特点

- 将经纬度坐标转换为详细地址
- 获取街道、道路名称
- 获取附近的POI（兴趣点）信息
- 获取湖泊、江河等水系信息
- 获取行政区划信息（省、市、区县）
- 支持HTTP/HTTPS请求
- 免费申请开发者Key

## 使用前准备

### 1. 申请天地图开发者Key

1. 访问天地图开发者平台：http://lbs.tianditu.gov.cn/
2. 注册成为天地图开发者
3. 申请服务许可（Key）
4. 获取开发者密钥（tk参数）

### 2. 配额限制

- 个人开发者和企业开发者都有服务调用量配额限制
- 如需更高服务调用量，可到控制台升级账号

## API接口详情

### 接口地址
```
http://api.tianditu.gov.cn/geocoder
```

### 请求方法
GET

### 请求参数

| 参数名 | 参数说明 | 参数类型 | 是否必填 | 备注 |
|--------|----------|----------|----------|------|
| lon | 经度坐标 | string | 是 | 坐标的x值 |
| lat | 纬度坐标 | string | 是 | 坐标的y值 |
| appkey | 网站的唯一编码 | string | 是 | 开发者Key |
| ver | 接口版本 | string | 是 | 固定为1 |
| type | 请求类型 | string | 是 | 固定为"geocode" |

### 请求示例

```http
GET http://api.tianditu.gov.cn/geocoder?postStr={'lon':116.37304,'lat':39.92594,'ver':1}&type=geocode&tk=您的密钥
```

### 返回数据结构

#### 响应格式
```json
{
  "status": "0",
  "msg": "OK",
  "result": {
    "formatted_address": "详细地址",
    "addressComponent": {
      "address": "最近地点信息",
      "address_distance": 距离最近地点的距离,
      "address_position": "此点在最近地点的方向",
      "city": "所在城市或区县",
      "poi": "距离最近的POI点",
      "poi_distance": 距离最近POI点的距离,
      "poi_position": "此点在最近POI点的方向",
      "road": "距离最近的路",
      "road_distance": 此点距离道路的距离
    },
    "location": {
      "lon": "经度",
      "lat": "纬度"
    }
  }
}
```

#### 返回参数说明

**响应接口参数：**

| 参数名 | 参数说明 | 参数类型 | 返回条件 | 备注 |
|--------|----------|----------|----------|------|
| result | 响应的具体信息 | Json | 有结果时返回 | - |
| status | 状态 | String | 必返回 | 0：正确，1：错误，404：出错 |
| msg | 响应信息 | String | 必返回 | OK：有信息 |

**result参数：**

| 参数名 | 参数说明 | 参数类型 | 返回条件 | 备注 |
|--------|----------|----------|----------|------|
| addressComponent | 此点的具体信息（分类） | Json | 必返回 | - |
| formatted_address | 详细地址 | String | 必返回 | - |
| location | 此点坐标 | Json | 必返回 | - |

**addressComponent参数：**

| 参数名 | 参数说明 | 参数类型 | 返回条件 | 备注 |
|--------|----------|----------|----------|------|
| address | 此点最近地点信息 | string | 必返回 | - |
| address_distance | 此点距离最近地点信息距离 | int | 必返回 | 单位：米 |
| address_position | 此点在最近地点信息方向 | string | 必返回 | - |
| city | 此点所在国家或城市或区县 | string | 必返回 | - |
| poi | 距离此点最近poi点 | string | 必返回 | - |
| poi_distance | 距离此点最近poi点的距离 | int | 必返回 | 单位：米 |
| poi_position | 此点在最近poi点的方向 | string | 必返回 | - |
| road | 距离此点最近的路 | string | 必返回 | - |
| road_distance | 此点距离此路的距离 | int | 必返回 | 单位：米 |

## Flutter代码实现

### 1. 添加依赖

在`pubspec.yaml`中添加HTTP请求库：

```yaml
dependencies:
  dio: ^5.3.2  # 或最新版本
```

### 2. 基础实现代码

```dart
import 'dart:convert';
import 'package:dio/dio.dart';

class TianDiTuService {
  static const String _baseUrl = 'http://api.tianditu.gov.cn/geocoder';
  static const String _appKey = '您的天地图密钥'; // 替换为实际的密钥
  
  final Dio _dio = Dio(BaseOptions(
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    contentType: Headers.formUrlEncodedContentType,
  ));

  /// 逆地理编码：根据经纬度获取地址信息
  /// [longitude] 经度
  /// [latitude] 纬度
  /// 返回地址信息，如果失败返回null
  Future<Map<String, dynamic>?> reverseGeocode(double longitude, double latitude) async {
    try {
      final String postStr = json.encode({
        'lon': longitude.toString(),
        'lat': latitude.toString(),
        'ver': '1'
      });
      
      final String url = '$_baseUrl?postStr=$postStr&type=geocode&tk=$_appKey';
      
      final response = await _dio.get(url);
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.data);
        
        if (data['status'] == '0') {
          return data['result'];
        } else {
          print('天地图API错误: ${data['msg']}');
          return null;
        }
      } else {
        print('HTTP请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('逆地理编码请求异常: $e');
      return null;
    }
  }

  /// 获取格式化的地址字符串
  Future<String?> getFormattedAddress(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['formatted_address'];
  }

  /// 获取道路名称
  Future<String?> getRoadName(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['road'];
  }

  /// 获取POI信息
  Future<String?> getPOIName(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['poi'];
  }

  /// 获取城市信息
  Future<String?> getCityName(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['city'];
  }

  /// 获取完整的地址组件信息
  Future<AddressComponent?> getAddressComponent(double longitude, double latitude) async {
    final result = await reverseGeocode(longitude, latitude);
    if (result != null && result['addressComponent'] != null) {
      return AddressComponent.fromJson(result['addressComponent']);
    }
    return null;
  }
}

/// 地址组件数据模型
class AddressComponent {
  final String? address;
  final int? addressDistance;
  final String? addressPosition;
  final String? city;
  final String? poi;
  final int? poiDistance;
  final String? poiPosition;
  final String? road;
  final int? roadDistance;

  AddressComponent({
    this.address,
    this.addressDistance,
    this.addressPosition,
    this.city,
    this.poi,
    this.poiDistance,
    this.poiPosition,
    this.road,
    this.roadDistance,
  });

  factory AddressComponent.fromJson(Map<String, dynamic> json) {
    return AddressComponent(
      address: json['address'],
      addressDistance: json['address_distance'],
      addressPosition: json['address_position'],
      city: json['city'],
      poi: json['poi'],
      poiDistance: json['poi_distance'],
      poiPosition: json['poi_position'],
      road: json['road'],
      roadDistance: json['road_distance'],
    );
  }

  @override
  String toString() {
    return 'AddressComponent{address: $address, city: $city, poi: $poi, road: $road}';
  }
}
```

### 3. 使用示例

```dart
class LocationService {
  final TianDiTuService _tianDiTuService = TianDiTuService();

  /// 根据经纬度获取地址信息
  Future<void> getLocationInfo(double longitude, double latitude) async {
    try {
      // 获取完整地址
      final formattedAddress = await _tianDiTuService.getFormattedAddress(longitude, latitude);
      print('完整地址: $formattedAddress');

      // 获取道路名称
      final roadName = await _tianDiTuService.getRoadName(longitude, latitude);
      print('道路名称: $roadName');

      // 获取POI信息
      final poiName = await _tianDiTuService.getPOIName(longitude, latitude);
      print('POI信息: $poiName');

      // 获取城市信息
      final cityName = await _tianDiTuService.getCityName(longitude, latitude);
      print('城市信息: $cityName');

      // 获取完整的地址组件
      final addressComponent = await _tianDiTuService.getAddressComponent(longitude, latitude);
      if (addressComponent != null) {
        print('地址组件: $addressComponent');
      }
    } catch (e) {
      print('获取位置信息失败: $e');
    }
  }
}
```

## 注意事项

1. **密钥安全**：不要将天地图密钥硬编码在客户端代码中，建议通过服务器代理或环境变量管理
2. **请求频率**：注意API调用频率限制，避免短时间内大量请求
3. **错误处理**：务必处理网络异常和API错误返回
4. **坐标系统**：确保传入的经纬度坐标系统正确
5. **数据缓存**：对于相同位置的请求可以考虑缓存结果，减少API调用

## 常见问题

### Q: 如何申请天地图开发者Key？
A: 访问 http://lbs.tianditu.gov.cn/ 注册开发者账号，然后申请服务许可即可获得Key。

### Q: API有调用次数限制吗？
A: 是的，天地图对个人和企业开发者都有配额限制，具体限制可在开发者控制台查看。

### Q: 支持哪些坐标系？
A: 天地图API支持WGS84、GCJ02等主流坐标系，具体以官方文档为准。

### Q: 返回的地址信息为空怎么办？
A: 可能是坐标位置偏远或坐标系不匹配，建议检查坐标的准确性和坐标系统。

## 更新日志

- 2025-01-19: 创建文档，包含基础API使用方法和Flutter代码示例
