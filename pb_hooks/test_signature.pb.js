// 测试AWS S3 Signature V4实现
// 这个文件用于验证签名算法的正确性

// 测试API端点
routerAdd("GET", "/api/test/signature", (c) => {
  console.log('收到签名测试请求');
  
  try {
    // 测试参数
    const testObjectKey = 'test/sample.jpg';
    const testMethod = 'PUT';
    const testExpiresIn = 900; // 15分钟
    
    // 生成预签名URL
    const presignedUrl = generatePresignedUrl(testMethod, testObjectKey, testExpiresIn);
    
    // 返回测试结果
    return c.json(200, {
      success: true,
      message: '签名算法测试成功',
      testData: {
        method: testMethod,
        objectKey: testObjectKey,
        expiresIn: testExpiresIn,
        presignedUrl: presignedUrl,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('签名测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '签名测试失败'
    });
  }
});

// 测试各个组件函数
routerAdd("GET", "/api/test/signature/components", (c) => {
  console.log('收到签名组件测试请求');
  
  try {
    const now = new Date();
    const amzDate = formatAmzDate(now);
    const dateStamp = amzDate.substring(0, 8);
    const testObjectKey = 'test/sample.jpg';
    const testExpiresIn = 900;
    
    // 测试各个组件
    const canonicalRequest = createCanonicalRequest('PUT', testObjectKey, amzDate, testExpiresIn);
    const stringToSign = createStringToSign(amzDate, dateStamp, canonicalRequest);
    const signature = calculateSignature(stringToSign, dateStamp);
    
    return c.json(200, {
      success: true,
      message: '签名组件测试成功',
      components: {
        amzDate: amzDate,
        dateStamp: dateStamp,
        canonicalRequest: canonicalRequest,
        stringToSign: stringToSign,
        signature: signature
      }
    });
    
  } catch (error) {
    console.error('签名组件测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '签名组件测试失败'
    });
  }
});

console.log('AWS S3签名测试服务已加载');
