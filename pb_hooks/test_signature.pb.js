// 测试AWS S3 Signature V4实现
// 这个文件用于验证签名算法的正确性

// 简化的测试，不依赖外部函数
routerAdd("GET", "/api/test/signature", (c) => {
  console.log('收到签名测试请求');

  try {
    // 简单测试，验证基本功能
    const testData = {
      message: "签名算法测试",
      timestamp: new Date().toISOString(),
      testObjectKey: 'test/sample.jpg',
      testMethod: 'PUT',
      testExpiresIn: 900
    };

    // 模拟预签名URL（实际的签名在generate_upload_url.pb.js中）
    const mockPresignedUrl = 'https://example.r2.cloudflarestorage.com/test-bucket/test/sample.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&...';
    
    // 返回测试结果
    return c.json(200, {
      success: true,
      message: '签名算法测试成功（模拟）',
      testData: testData,
      mockPresignedUrl: mockPresignedUrl,
      note: '实际签名在generate_upload_url.pb.js中实现'
    });
    
  } catch (error) {
    console.error('签名测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '签名测试失败'
    });
  }
});

// 测试各个组件函数（简化版）
routerAdd("GET", "/api/test/signature/components", (c) => {
  console.log('收到签名组件测试请求');

  try {
    const now = new Date();
    const testData = {
      timestamp: now.toISOString(),
      testObjectKey: 'test/sample.jpg',
      testExpiresIn: 900,
      note: '组件测试简化版，实际实现在generate_upload_url.pb.js中'
    };

    return c.json(200, {
      success: true,
      message: '签名组件测试成功（简化版）',
      components: testData
    });
    
  } catch (error) {
    console.error('签名组件测试失败:', error);
    return c.json(500, {
      success: false,
      error: error.message || '签名组件测试失败'
    });
  }
});

console.log('AWS S3签名测试服务已加载');
