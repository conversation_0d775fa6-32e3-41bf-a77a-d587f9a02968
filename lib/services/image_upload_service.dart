import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import '../config/r2_config.dart';
import 'presigned_upload_service.dart';

/// 图片上传服务
///
/// 功能：
/// 1. 图片压缩和优化
/// 2. 生成缩略图
/// 3. 通过预签名URL上传到Cloudflare R2
/// 4. 返回图片URL
class ImageUploadService {
  final PresignedUploadService _presignedService = PresignedUploadService();

  /// 上传单张图片
  /// [imageFile] 原始图片文件
  /// [userId] 用户ID
  /// [spotId] 钓点ID
  /// 返回上传结果，包含原图和缩略图URL
  Future<ImageUploadResult?> uploadImage({
    required File imageFile,
    required String userId,
    required String spotId,
  }) async {
    try {
      // 1. 验证文件
      if (!await _validateImage(imageFile)) {
        debugPrint('图片验证失败');
        return null;
      }

      // 2. 读取和处理图片
      final imageBytes = await imageFile.readAsBytes();
      final originalImage = img.decodeImage(imageBytes);

      if (originalImage == null) {
        debugPrint('无法解码图片');
        return null;
      }

      // 3. 压缩原图
      final compressedImage = _compressImage(originalImage);
      final compressedBytes = img.encodeJpg(
        compressedImage,
        quality: R2Config.imageQuality,
      );

      // 4. 生成缩略图
      final thumbnail = _generateThumbnail(originalImage);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: 80);

      // 5. 生成文件名
      final fileName = path.basename(imageFile.path);

      // 6. 获取预签名URL
      final presignedResponse = await _presignedService.getPresignedUrl(
        spotId: spotId,
        fileName: fileName,
        fileType: 'image/jpeg',
      );

      if (presignedResponse == null) {
        debugPrint('获取预签名URL失败');
        return null;
      }

      // 7. 上传原图到R2
      final uploadSuccess = await _presignedService.uploadWithPresignedUrl(
        presignedUrl: presignedResponse.uploadUrl,
        fileBytes: compressedBytes,
        contentType: 'image/jpeg',
      );

      if (!uploadSuccess) {
        debugPrint('原图上传失败');
        return null;
      }

      // 8. 上传缩略图到R2（如果有缩略图URL）
      bool thumbnailUploadSuccess = true;
      if (presignedResponse.thumbnailUploadUrl != null) {
        thumbnailUploadSuccess = await _presignedService.uploadWithPresignedUrl(
          presignedUrl: presignedResponse.thumbnailUploadUrl!,
          fileBytes: thumbnailBytes,
          contentType: 'image/jpeg',
        );

        if (!thumbnailUploadSuccess) {
          debugPrint('缩略图上传失败');
          // 原图已上传成功，缩略图失败不影响主要功能
        }
      }

      debugPrint('图片上传成功: ${presignedResponse.publicUrl}');

      // 9. 返回结果
      return ImageUploadResult(
        url: presignedResponse.publicUrl,
        path: presignedResponse.filePath,
        thumbnailUrl:
            presignedResponse.thumbnailPublicUrl ?? presignedResponse.publicUrl,
        thumbnailPath:
            presignedResponse.thumbnailPath ?? presignedResponse.filePath,
        fileSize: compressedBytes.length,
        mimeType: 'image/jpeg',
      );
    } catch (e) {
      debugPrint('图片上传异常: $e');
      return null;
    }
  }

  /// 批量上传图片
  Future<List<ImageUploadResult>> uploadImages({
    required List<File> imageFiles,
    required String userId,
    required String spotId,
  }) async {
    final results = <ImageUploadResult>[];

    for (final imageFile in imageFiles) {
      final result = await uploadImage(
        imageFile: imageFile,
        userId: userId,
        spotId: spotId,
      );

      if (result != null) {
        results.add(result);
      }
    }

    return results;
  }

  /// 验证图片文件
  Future<bool> _validateImage(File imageFile) async {
    try {
      // 检查文件是否存在
      if (!await imageFile.exists()) {
        return false;
      }

      // 检查文件大小
      final fileSize = await imageFile.length();
      if (fileSize > R2Config.maxImageSize) {
        debugPrint('图片文件过大: ${fileSize}bytes');
        return false;
      }

      // 检查文件扩展名
      final extension = path
          .extension(imageFile.path)
          .toLowerCase()
          .replaceAll('.', '');
      if (!R2Config.allowedExtensions.contains(extension)) {
        debugPrint('不支持的图片格式: $extension');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('图片验证异常: $e');
      return false;
    }
  }

  /// 压缩图片
  img.Image _compressImage(img.Image image) {
    int width = image.width;
    int height = image.height;

    // 计算缩放比例
    if (width > R2Config.maxImageWidth || height > R2Config.maxImageHeight) {
      final widthRatio = R2Config.maxImageWidth / width;
      final heightRatio = R2Config.maxImageHeight / height;
      final ratio = widthRatio < heightRatio ? widthRatio : heightRatio;

      width = (width * ratio).round();
      height = (height * ratio).round();
    }

    return img.copyResize(image, width: width, height: height);
  }

  /// 生成缩略图
  img.Image _generateThumbnail(img.Image image) {
    const thumbnailSize = 300;

    // 计算缩略图尺寸（保持宽高比）
    int width, height;
    if (image.width > image.height) {
      width = thumbnailSize;
      height = (image.height * thumbnailSize / image.width).round();
    } else {
      height = thumbnailSize;
      width = (image.width * thumbnailSize / image.height).round();
    }

    return img.copyResize(image, width: width, height: height);
  }
}
