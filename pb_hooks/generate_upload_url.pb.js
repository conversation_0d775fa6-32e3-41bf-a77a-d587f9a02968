// PocketBase Hook - 简化的图片上传URL生成
// 由于PocketBase不支持完整的AWS签名，我们使用简化方案

// R2配置
const R2_CONFIG = {
  endpoint: 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com',
  bucketName: 'fishing-app'
};

// 生成预签名上传URL的API端点
routerAdd("POST", "/api/generate-upload-url", (c) => {
  console.log('收到预签名URL请求');

  try {
    // 验证用户身份
    const authRecord = c.get("authRecord");
    console.log('认证记录:', authRecord ? authRecord.id : 'null');

    if (!authRecord) {
      console.log('用户未认证');
      return c.json(401, { error: "未授权访问" });
    }

    // 获取请求参数
    const data = c.bind({});
    console.log('请求数据:', JSON.stringify(data));

    const { spotId, fileName, fileType } = data;
    console.log('解析参数:', { spotId, fileName, fileType });

    // 验证参数
    if (!spotId || !fileName || !fileType) {
      console.log('参数验证失败');
      return c.json(400, { error: "缺少必要参数: spotId, fileName, fileType" });
    }

    // 验证钓点是否存在且属于用户
    try {
      const spot = $app.dao().findRecordById("fishing_spots", spotId);
      if (!spot || spot.get("user_id") !== authRecord.id) {
        console.log('钓点不存在或无权限');
        return c.json(403, { error: "钓点不存在或无权限" });
      }
    } catch (spotError) {
      console.error('查找钓点失败:', spotError);
      return c.json(404, { error: "钓点不存在" });
    }

    // 生成文件路径
    const timestamp = Date.now();
    const uuid = generateShortUuid();
    const extension = getFileExtension(fileName);
    const filePath = `spots/${authRecord.id}/${spotId}/${timestamp}_${uuid}.${extension}`;
    const thumbnailPath = `spots/${authRecord.id}/${spotId}/${timestamp}_${uuid}_thumb.${extension}`;

    // 由于PocketBase限制，我们返回上传所需的信息
    // 客户端需要使用自己的签名逻辑或直接上传

    // 生成公开访问URL
    const publicUrl = `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${filePath}`;
    const thumbnailPublicUrl = `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${thumbnailPath}`;

    // 返回上传信息（客户端需要自己处理签名）
    return c.json(200, {
      // 注意：这里不返回真正的预签名URL，而是返回上传所需的信息
      uploadUrl: `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${filePath}`,
      filePath: filePath,
      publicUrl: publicUrl,
      thumbnailUploadUrl: `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${thumbnailPath}`,
      thumbnailPath: thumbnailPath,
      thumbnailPublicUrl: thumbnailPublicUrl,
      // 返回R2配置信息供客户端使用
      r2Config: {
        endpoint: R2_CONFIG.endpoint,
        bucketName: R2_CONFIG.bucketName
      }
    });

  } catch (error) {
    console.error('生成预签名URL失败:', error);
    return c.json(500, { error: error.message || '服务器内部错误' });
  }
});

// 简化的文件路径生成（不需要复杂的AWS签名）

// 生成短UUID
function generateShortUuid() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
}

// 获取文件扩展名
function getFileExtension(filename) {
  const parts = filename.split('.');
  return parts.length > 1 ? parts.pop().toLowerCase() : 'jpg';
}

console.log('预签名URL生成服务已加载');
