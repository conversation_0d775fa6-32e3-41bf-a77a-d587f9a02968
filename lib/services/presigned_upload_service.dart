import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'service_locator.dart';
import '../config/app_config.dart';
import '../config/pocketbase_config.dart';

/// 预签名URL上传服务
class PresignedUploadService {
  
  /// 获取预签名上传URL
  Future<PresignedUrlResponse?> getPresignedUrl({
    required String spotId,
    required String fileName,
    required String fileType,
  }) async {
    try {
      final user = Services.auth.currentUser;
      if (user == null) {
        debugPrint('用户未登录');
        return null;
      }
      
      // 构建请求URL
      final baseUrl = AppConfig.instance.pocketBaseUrl;
      final url = '$baseUrl/api/generate-upload-url';
      
      // 获取PocketBase认证token
      final pb = PocketBaseConfig.instance.client;
      final token = pb.authStore.token;

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'spotId': spotId,
          'fileName': fileName,
          'fileType': fileType,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PresignedUrlResponse.fromJson(data);
      } else {
        debugPrint('获取预签名URL失败: ${response.statusCode} - ${response.body}');
        return null;
      }
      
    } catch (e) {
      debugPrint('获取预签名URL异常: $e');
      return null;
    }
  }
  
  /// 使用预签名URL上传文件
  Future<bool> uploadWithPresignedUrl({
    required String presignedUrl,
    required Uint8List fileBytes,
    required String contentType,
  }) async {
    try {
      final response = await http.put(
        Uri.parse(presignedUrl),
        headers: {
          'Content-Type': contentType,
          'Content-Length': fileBytes.length.toString(),
        },
        body: fileBytes,
      );
      
      if (response.statusCode == 200) {
        debugPrint('文件上传成功');
        return true;
      } else {
        debugPrint('文件上传失败: ${response.statusCode} - ${response.body}');
        return false;
      }
      
    } catch (e) {
      debugPrint('预签名URL上传异常: $e');
      return false;
    }
  }
  
  /// 批量获取预签名URL
  Future<List<PresignedUrlResponse>> getBatchPresignedUrls({
    required String spotId,
    required List<String> fileNames,
    required List<String> fileTypes,
  }) async {
    final results = <PresignedUrlResponse>[];
    
    for (int i = 0; i < fileNames.length; i++) {
      final result = await getPresignedUrl(
        spotId: spotId,
        fileName: fileNames[i],
        fileType: fileTypes[i],
      );
      
      if (result != null) {
        results.add(result);
      }
    }
    
    return results;
  }
  
  /// 保存照片记录到PocketBase
  Future<bool> savePhotoRecord({
    required String spotId,
    required String userId,
    required String filename,
    required String url,
    required String? thumbnailUrl,
    required String storagePath,
    required String? thumbnailPath,
    required int sortOrder,
    required int fileSize,
    required String mimeType,
  }) async {
    try {
      final user = Services.auth.currentUser;
      if (user == null) return false;
      
      final baseUrl = AppConfig.instance.pocketBaseUrl;
      final apiUrl = '$baseUrl/api/collections/spot_photos/records';
      
      // 获取PocketBase认证token
      final pb = PocketBaseConfig.instance.client;
      final token = pb.authStore.token;

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'spot_id': spotId,
          'user_id': userId,
          'filename': filename,
          'url': url,
          'thumbnail_url': thumbnailUrl,
          'storage_path': storagePath,
          'thumbnail_path': thumbnailPath,
          'type': 'normal',
          'sort_order': sortOrder,
          'file_size': fileSize,
          'mime_type': mimeType,
        }),
      );
      
      if (response.statusCode == 200) {
        debugPrint('照片记录保存成功');
        return true;
      } else {
        debugPrint('照片记录保存失败: ${response.statusCode} - ${response.body}');
        return false;
      }
      
    } catch (e) {
      debugPrint('保存照片记录异常: $e');
      return false;
    }
  }
  
  /// 批量保存照片记录
  Future<bool> saveBatchPhotoRecords({
    required String spotId,
    required String userId,
    required List<PhotoRecordData> photoData,
  }) async {
    try {
      final user = Services.auth.currentUser;
      if (user == null) return false;
      
      final baseUrl = AppConfig.instance.pocketBaseUrl;
      final apiUrl = '$baseUrl/api/photos/batch';
      
      final photos = photoData.map((data) => data.toJson()).toList();
      
      // 获取PocketBase认证token
      final pb = PocketBaseConfig.instance.client;
      final token = pb.authStore.token;

      debugPrint('批量照片上传 - Token: ${token.isNotEmpty ? token.substring(0, 20) + '...' : 'empty'}');
      debugPrint('批量照片上传 - 完整Token: $token'); // 临时调试，显示完整token
      debugPrint('批量照片上传 - API URL: $apiUrl');
      debugPrint('批量照片上传 - 照片数量: ${photos.length}');

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'spotId': spotId,
          'photos': photos,
        }),
      );
      
      if (response.statusCode == 200) {
        debugPrint('批量照片记录保存成功');
        return true;
      } else {
        debugPrint('批量照片记录保存失败: ${response.statusCode} - ${response.body}');
        return false;
      }
      
    } catch (e) {
      debugPrint('批量保存照片记录异常: $e');
      return false;
    }
  }
}

/// 预签名URL响应
class PresignedUrlResponse {
  final String uploadUrl;
  final String filePath;
  final String publicUrl;
  final String? thumbnailUploadUrl;
  final String? thumbnailPath;
  final String? thumbnailPublicUrl;
  
  PresignedUrlResponse({
    required this.uploadUrl,
    required this.filePath,
    required this.publicUrl,
    this.thumbnailUploadUrl,
    this.thumbnailPath,
    this.thumbnailPublicUrl,
  });
  
  factory PresignedUrlResponse.fromJson(Map<String, dynamic> json) {
    return PresignedUrlResponse(
      uploadUrl: json['uploadUrl'],
      filePath: json['filePath'],
      publicUrl: json['publicUrl'],
      thumbnailUploadUrl: json['thumbnailUploadUrl'],
      thumbnailPath: json['thumbnailPath'],
      thumbnailPublicUrl: json['thumbnailPublicUrl'],
    );
  }
}

/// 照片记录数据
class PhotoRecordData {
  final String filename;
  final String url;
  final String? thumbnailUrl;
  final String storagePath;
  final String? thumbnailPath;
  final int sortOrder;
  final int fileSize;
  final String mimeType;
  
  PhotoRecordData({
    required this.filename,
    required this.url,
    this.thumbnailUrl,
    required this.storagePath,
    this.thumbnailPath,
    required this.sortOrder,
    required this.fileSize,
    required this.mimeType,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'filename': filename,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'storage_path': storagePath,
      'thumbnail_path': thumbnailPath,
      'sort_order': sortOrder,
      'file_size': fileSize,
      'mime_type': mimeType,
    };
  }
}
