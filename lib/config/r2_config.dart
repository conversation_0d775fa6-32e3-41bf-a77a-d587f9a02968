/// Cloudflare R2 配置
/// 
/// 安全注意事项：
/// 1. 生产环境中，这些配置应该从环境变量或安全配置服务获取
/// 2. 不要将敏感信息硬编码在客户端代码中
/// 3. 考虑使用PocketBase后端代理上传请求
class R2Config {
  // 生产环境建议：通过PocketBase后端获取临时凭证
  static const String bucketName = 'fishing-app';
  static const String region = 'auto';
  static const String endpoint = 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com';
  
  // 安全建议：这些密钥应该在后端管理，客户端通过API获取临时凭证
  // 当前为演示目的，实际部署时应移除
  static const String accessKeyId = '2975b9f0b5ed91f29bec884c3fdcd4c8';
  static const String secretAccessKey = '****************************************************************';
  
  // 图片配置
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
  static const int imageQuality = 85; // 压缩质量
  static const int maxImageWidth = 1920;
  static const int maxImageHeight = 1080;
  
  // URL配置
  static const String cdnDomain = 'https://fishing-app.your-domain.com'; // 可选：自定义域名
  
  /// 生成图片存储路径
  /// 格式：spots/{userId}/{spotId}/{timestamp}_{uuid}.{ext}
  static String generateImagePath(String userId, String spotId, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = _generateShortUuid();
    return 'spots/$userId/$spotId/${timestamp}_$uuid.$extension';
  }
  
  /// 生成缩略图路径
  static String generateThumbnailPath(String originalPath) {
    final parts = originalPath.split('.');
    final extension = parts.last;
    final pathWithoutExt = parts.sublist(0, parts.length - 1).join('.');
    return '${pathWithoutExt}_thumb.$extension';
  }
  
  /// 生成短UUID（用于文件名）
  static String _generateShortUuid() {
    return DateTime.now().millisecondsSinceEpoch.toRadixString(36) +
           (DateTime.now().microsecond % 1000).toRadixString(36);
  }
  
  /// 获取完整的图片URL
  static String getImageUrl(String path) {
    if (cdnDomain.isNotEmpty) {
      return '$cdnDomain/$path';
    }
    return '$endpoint/$bucketName/$path';
  }
}

/// 图片上传结果
class ImageUploadResult {
  final String url;
  final String path;
  final String thumbnailUrl;
  final String thumbnailPath;
  final int fileSize;
  final String mimeType;
  
  ImageUploadResult({
    required this.url,
    required this.path,
    required this.thumbnailUrl,
    required this.thumbnailPath,
    required this.fileSize,
    required this.mimeType,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'path': path,
      'thumbnail_url': thumbnailUrl,
      'thumbnail_path': thumbnailPath,
      'file_size': fileSize,
      'mime_type': mimeType,
    };
  }
  
  factory ImageUploadResult.fromJson(Map<String, dynamic> json) {
    return ImageUploadResult(
      url: json['url'],
      path: json['path'],
      thumbnailUrl: json['thumbnail_url'],
      thumbnailPath: json['thumbnail_path'],
      fileSize: json['file_size'],
      mimeType: json['mime_type'],
    );
  }
}
