# AWS S3 Signature V4 实现说明

## 概述

本项目现已实现完整的AWS S3 Signature V4算法，用于生成Cloudflare R2的预签名上传URL。这替代了之前的模拟实现，提供了真正的文件上传功能。

## 实现特性

### ✅ 已实现功能

1. **完整的AWS S3 Signature V4算法**
   - 规范请求创建
   - 待签名字符串生成
   - HMAC-SHA256签名计算
   - 预签名URL构建

2. **双文件上传支持**
   - 原图上传
   - 缩略图上传
   - 独立的预签名URL

3. **安全性**
   - 15分钟URL过期时间
   - 正确的签名验证
   - 安全的密钥处理

4. **错误处理**
   - 网络错误重试
   - 详细的错误日志
   - 优雅的降级处理

## 文件结构

```
pb_hooks/
├── generate_upload_url.pb.js    # 主要的预签名URL生成服务
└── test_signature.pb.js         # 签名算法测试工具

lib/services/
├── image_upload_service.dart    # 图片上传服务（已更新）
└── presigned_upload_service.dart # 预签名URL服务
```

## API端点

### 1. 生成预签名URL
```
POST /api/generate-upload-url
```

**请求体：**
```json
{
  "spotId": "钓点ID",
  "fileName": "image.jpg",
  "fileType": "image/jpeg"
}
```

**响应：**
```json
{
  "uploadUrl": "https://...预签名URL",
  "filePath": "spots/userId/spotId/timestamp_uuid.jpg",
  "publicUrl": "https://...公开访问URL",
  "thumbnailUploadUrl": "https://...缩略图预签名URL",
  "thumbnailPath": "spots/userId/spotId/timestamp_uuid_thumb.jpg",
  "thumbnailPublicUrl": "https://...缩略图公开URL",
  "expiresIn": 900,
  "timestamp": 1234567890
}
```

### 2. 测试端点
```
GET /api/test/signature           # 测试完整签名流程
GET /api/test/signature/components # 测试签名组件
```

## 使用方法

### 客户端上传流程

1. **获取预签名URL**
```dart
final response = await presignedService.getPresignedUrl(
  spotId: spotId,
  fileName: fileName,
  fileType: 'image/jpeg',
);
```

2. **上传文件**
```dart
final success = await presignedService.uploadWithPresignedUrl(
  presignedUrl: response.uploadUrl,
  fileBytes: imageBytes,
  contentType: 'image/jpeg',
);
```

3. **使用公开URL**
```dart
final imageUrl = response.publicUrl;
// 用于显示图片
```

## 配置说明

### R2配置（pb_hooks/generate_upload_url.pb.js）

```javascript
const R2_CONFIG = {
  endpoint: 'https://your-account-id.r2.cloudflarestorage.com',
  bucketName: 'your-bucket-name',
  region: 'auto',
  accessKeyId: 'your-access-key-id',
  secretAccessKey: 'your-secret-access-key'
};
```

⚠️ **安全提醒：** 生产环境中应从环境变量获取敏感配置：

```javascript
const R2_CONFIG = {
  endpoint: $os.getenv('R2_ENDPOINT'),
  bucketName: $os.getenv('R2_BUCKET_NAME'),
  region: $os.getenv('R2_REGION') || 'auto',
  accessKeyId: $os.getenv('R2_ACCESS_KEY_ID'),
  secretAccessKey: $os.getenv('R2_SECRET_ACCESS_KEY')
};
```

## 测试验证

### 1. 测试签名算法
```bash
curl http://localhost:8090/api/test/signature
```

### 2. 测试组件功能
```bash
curl http://localhost:8090/api/test/signature/components
```

### 3. 测试完整上传流程
在Flutter应用中选择图片并上传，查看控制台日志。

## 故障排除

### 常见问题

1. **SignatureDoesNotMatch错误**
   - 检查R2配置是否正确
   - 验证时间同步
   - 确认密钥格式

2. **上传失败**
   - 检查网络连接
   - 验证预签名URL格式
   - 查看R2存储桶权限

3. **URL过期**
   - 默认15分钟过期
   - 可调整expiresIn参数

### 调试日志

启用详细日志：
```javascript
console.log('调试信息:', {
  canonicalRequest,
  stringToSign,
  signature
});
```

## 性能优化

1. **并发上传**：原图和缩略图可并行上传
2. **缓存优化**：预签名URL可短时间缓存
3. **重试机制**：网络失败时自动重试

## 安全考虑

1. **密钥管理**：使用环境变量存储敏感信息
2. **URL过期**：合理设置过期时间
3. **权限控制**：确保用户只能上传自己的文件
4. **文件验证**：验证文件类型和大小

## 更新日志

- **v1.0.0**: 实现完整的AWS S3 Signature V4算法
- **v1.0.1**: 添加缩略图支持
- **v1.0.2**: 增强错误处理和日志记录
