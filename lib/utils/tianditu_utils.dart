import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

/// 天地图工具类
/// 统一管理天地图密钥和API调用
class TianDiTuUtils {
  /// 天地图密钥（统一管理）
  static const String apiKey = "23fee0544e2033fa1dbc0d6ae7bf30ad";
  
  /// 逆地理编码API基础URL
  static const String _geocodeBaseUrl = 'http://api.tianditu.gov.cn/geocoder';

  /// 获取天地图密钥
  /// 用于瓦片URL和API调用
  static String get key => apiKey;

  /// 逆地理编码：根据经纬度获取地址信息
  /// [longitude] 经度
  /// [latitude] 纬度
  /// 返回地址信息，如果失败返回null
  static Future<Map<String, dynamic>?> reverseGeocode(
    double longitude, 
    double latitude
  ) async {
    try {
      final String postStr = json.encode({
        'lon': longitude.toString(),
        'lat': latitude.toString(),
        'ver': '1'
      });
      
      final String url = '$_geocodeBaseUrl?postStr=$postStr&type=geocode&tk=$apiKey';
      
      debugPrint('天地图API请求: $url');
      
      final response = await http.get(Uri.parse(url)).timeout(
        const Duration(seconds: 10),
      );
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        debugPrint('天地图API响应: $data');
        
        if (data['status'] == '0') {
          return data['result'];
        } else {
          debugPrint('天地图API错误: ${data['msg']}');
          return null;
        }
      } else {
        debugPrint('HTTP请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('逆地理编码请求异常: $e');
      return null;
    }
  }

  /// 获取最佳的位置名称
  /// 优先级：POI > 道路 > 格式化地址的关键部分
  static Future<String?> getBestLocationName(
    double longitude, 
    double latitude
  ) async {
    try {
      final result = await reverseGeocode(longitude, latitude);
      if (result == null) return null;

      final addressComponent = result['addressComponent'];
      if (addressComponent == null) return null;

      // 优先使用POI名称
      String? poi = addressComponent['poi'];
      if (poi != null && poi.trim().isNotEmpty) {
        return poi.trim();
      }

      // 其次使用道路名称
      String? road = addressComponent['road'];
      if (road != null && road.trim().isNotEmpty) {
        return road.trim();
      }

      // 最后使用格式化地址的关键部分
      String? formattedAddress = result['formatted_address'];
      if (formattedAddress != null && formattedAddress.trim().isNotEmpty) {
        // 尝试提取地址中的关键部分
        List<String> parts = formattedAddress.split('');
        if (parts.isNotEmpty) {
          String lastPart = parts.last.trim();
          if (lastPart.isNotEmpty) {
            return lastPart;
          }
        }
        return formattedAddress.trim();
      }

      return null;
    } catch (e) {
      debugPrint('获取最佳位置名称失败: $e');
      return null;
    }
  }

  /// 获取格式化的地址字符串
  static Future<String?> getFormattedAddress(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['formatted_address'];
  }

  /// 获取道路名称
  static Future<String?> getRoadName(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['road'];
  }

  /// 获取POI信息
  static Future<String?> getPOIName(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['poi'];
  }

  /// 获取城市信息
  static Future<String?> getCityName(
    double longitude, 
    double latitude
  ) async {
    final result = await reverseGeocode(longitude, latitude);
    return result?['addressComponent']?['city'];
  }

  /// 构建瓦片URL模板
  /// [isVector] 是否为矢量图
  /// [isAnnotation] 是否为注记层
  /// 注意：返回的URL模板使用{k}占位符，需要通过additionalOptions传递密钥
  static String buildTileUrlTemplate({
    required bool isVector,
    required bool isAnnotation,
  }) {
    if (isAnnotation) {
      // 注记层
      return isVector
          ? "https://t{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}"
          : "https://t{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}";
    } else {
      // 底图层
      return isVector
          ? "https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}"
          : "https://t{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={k}";
    }
  }

  /// 获取天地图子域名列表
  static List<String> get subdomains => const ['0', '1', '2', '3', '4', '5', '6', '7'];
}

/// 地址组件数据模型
class TianDiTuAddressComponent {
  final String? address;
  final int? addressDistance;
  final String? addressPosition;
  final String? city;
  final String? poi;
  final int? poiDistance;
  final String? poiPosition;
  final String? road;
  final int? roadDistance;

  TianDiTuAddressComponent({
    this.address,
    this.addressDistance,
    this.addressPosition,
    this.city,
    this.poi,
    this.poiDistance,
    this.poiPosition,
    this.road,
    this.roadDistance,
  });

  factory TianDiTuAddressComponent.fromJson(Map<String, dynamic> json) {
    return TianDiTuAddressComponent(
      address: json['address'],
      addressDistance: json['address_distance'],
      addressPosition: json['address_position'],
      city: json['city'],
      poi: json['poi'],
      poiDistance: json['poi_distance'],
      poiPosition: json['poi_position'],
      road: json['road'],
      roadDistance: json['road_distance'],
    );
  }

  @override
  String toString() {
    return 'TianDiTuAddressComponent{address: $address, city: $city, poi: $poi, road: $road}';
  }
}
