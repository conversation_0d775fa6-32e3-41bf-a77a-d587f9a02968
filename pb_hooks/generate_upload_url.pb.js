// PocketBase Hook - 完整的AWS S3 Signature V4实现
// 用于生成Cloudflare R2预签名上传URL

// R2配置 - 生产环境应从环境变量获取
const R2_CONFIG = {
  endpoint: 'https://ab76374a6921dbc071ecdda63a033ec1.r2.cloudflarestorage.com',
  bucketName: 'fishing-app',
  region: 'auto',
  // 注意：这些密钥应该从环境变量或安全存储获取
  accessKeyId: '2975b9f0b5ed91f29bec884c3fdcd4c8',
  secretAccessKey: '****************************************************************'
};

// 生成预签名上传URL的API端点
routerAdd("POST", "/api/generate-upload-url", (c) => {
  console.log('收到预签名URL请求');

  try {
    // 验证用户身份 - 增强调试
    const authRecord = c.get("authRecord");
    const authHeader = c.request().header.get("Authorization");

    console.log('认证记录:', authRecord ? authRecord.id : 'null');
    console.log('Authorization头:', authHeader ? authHeader.substring(0, 20) + '...' : 'null');
    console.log('请求方法:', c.request().method);
    console.log('请求路径:', c.request().url.pathname);

    if (!authRecord) {
      console.log('认证失败，返回401');
      return c.json(401, {
        error: "未授权访问",
        debug: {
          hasAuthRecord: !!authRecord,
          hasAuthHeader: !!authHeader,
          method: c.request().method,
          path: c.request().url.pathname
        }
      });
    }

    // 获取请求参数
    const data = c.bind({});
    console.log('请求数据:', JSON.stringify(data));

    const { spotId, fileName, fileType } = data;
    console.log('解析参数:', { spotId, fileName, fileType });

    // 验证参数
    if (!spotId || !fileName || !fileType) {
      console.log('参数验证失败');
      return c.json(400, { error: "缺少必要参数: spotId, fileName, fileType" });
    }

    // 验证钓点是否存在且属于用户
    try {
      const spot = $app.dao().findRecordById("fishing_spots", spotId);
      if (!spot || spot.get("user_id") !== authRecord.id) {
        console.log('钓点不存在或无权限');
        return c.json(403, { error: "钓点不存在或无权限" });
      }
    } catch (spotError) {
      console.error('查找钓点失败:', spotError);
      return c.json(404, { error: "钓点不存在" });
    }

    // 生成文件路径
    const timestamp = Date.now();
    const uuid = generateShortUuid();
    const extension = getFileExtension(fileName);
    const filePath = `spots/${authRecord.id}/${spotId}/${timestamp}_${uuid}.${extension}`;
    const thumbnailPath = `spots/${authRecord.id}/${spotId}/${timestamp}_${uuid}_thumb.${extension}`;

    // 生成预签名URL（15分钟有效期）
    const expiresIn = 15 * 60; // 15分钟
    const presignedUrl = generatePresignedUrl('PUT', filePath, expiresIn);
    const thumbnailPresignedUrl = generatePresignedUrl('PUT', thumbnailPath, expiresIn);

    // 生成公开访问URL
    const publicUrl = `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${filePath}`;
    const thumbnailPublicUrl = `${R2_CONFIG.endpoint}/${R2_CONFIG.bucketName}/${thumbnailPath}`;

    // 返回完整的预签名URL信息
    return c.json(200, {
      uploadUrl: presignedUrl,
      filePath: filePath,
      publicUrl: publicUrl,
      thumbnailUploadUrl: thumbnailPresignedUrl,
      thumbnailPath: thumbnailPath,
      thumbnailPublicUrl: thumbnailPublicUrl,
      expiresIn: expiresIn,
      timestamp: timestamp
    });

  } catch (error) {
    console.error('生成预签名URL失败:', error);
    return c.json(500, { error: error.message || '服务器内部错误' });
  }
});

// ==================== AWS S3 Signature V4 实现 ====================

/**
 * 生成AWS S3 Signature V4预签名URL
 * @param {string} method - HTTP方法 (GET, PUT, POST等)
 * @param {string} objectKey - S3对象键
 * @param {number} expiresIn - 过期时间（秒）
 * @returns {string} 预签名URL
 */
function generatePresignedUrl(method, objectKey, expiresIn) {
  const now = new Date();
  const amzDate = formatAmzDate(now);
  const dateStamp = amzDate.substring(0, 8);

  // 构建规范请求
  const canonicalRequest = createCanonicalRequest(method, objectKey, amzDate, expiresIn);

  // 创建待签名字符串
  const stringToSign = createStringToSign(amzDate, dateStamp, canonicalRequest);

  // 计算签名
  const signature = calculateSignature(stringToSign, dateStamp);

  // 构建预签名URL
  return buildPresignedUrl(method, objectKey, amzDate, expiresIn, signature);
}

/**
 * 格式化AWS日期时间
 * @param {Date} date - 日期对象
 * @returns {string} ISO格式的日期时间字符串
 */
function formatAmzDate(date) {
  return date.toISOString().replace(/[:\-]|\.\d{3}/g, '');
}

/**
 * 创建规范请求
 * @param {string} method - HTTP方法
 * @param {string} objectKey - S3对象键
 * @param {string} amzDate - AWS格式日期
 * @param {number} expiresIn - 过期时间
 * @returns {string} 规范请求字符串
 */
function createCanonicalRequest(method, objectKey, amzDate, expiresIn) {
  const canonicalUri = '/' + encodeURIComponent(objectKey).replace(/%2F/g, '/');

  // 查询参数
  const queryParams = {
    'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
    'X-Amz-Credential': `${R2_CONFIG.accessKeyId}/${amzDate.substring(0, 8)}/${R2_CONFIG.region}/s3/aws4_request`,
    'X-Amz-Date': amzDate,
    'X-Amz-Expires': expiresIn.toString(),
    'X-Amz-SignedHeaders': 'host'
  };

  const canonicalQueryString = Object.keys(queryParams)
    .sort()
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  const canonicalHeaders = `host:${getHostFromEndpoint(R2_CONFIG.endpoint)}\n`;
  const signedHeaders = 'host';
  const payloadHash = 'UNSIGNED-PAYLOAD';

  return [
    method,
    canonicalUri,
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    payloadHash
  ].join('\n');
}

/**
 * 创建待签名字符串
 * @param {string} amzDate - AWS格式日期
 * @param {string} dateStamp - 日期戳
 * @param {string} canonicalRequest - 规范请求
 * @returns {string} 待签名字符串
 */
function createStringToSign(amzDate, dateStamp, canonicalRequest) {
  const algorithm = 'AWS4-HMAC-SHA256';
  const credentialScope = `${dateStamp}/${R2_CONFIG.region}/s3/aws4_request`;
  const hashedCanonicalRequest = $security.sha256(canonicalRequest);

  return [
    algorithm,
    amzDate,
    credentialScope,
    hashedCanonicalRequest
  ].join('\n');
}

/**
 * 计算签名
 * @param {string} stringToSign - 待签名字符串
 * @param {string} dateStamp - 日期戳
 * @returns {string} 十六进制签名
 */
function calculateSignature(stringToSign, dateStamp) {
  // 生成签名密钥
  const kDate = $security.hs256(dateStamp, 'AWS4' + R2_CONFIG.secretAccessKey);
  const kRegion = $security.hs256(R2_CONFIG.region, kDate);
  const kService = $security.hs256('s3', kRegion);
  const kSigning = $security.hs256('aws4_request', kService);

  // 计算最终签名
  const signature = $security.hs256(stringToSign, kSigning);

  // 转换为十六进制字符串
  return bytesToHex(signature);
}

/**
 * 构建预签名URL
 * @param {string} method - HTTP方法
 * @param {string} objectKey - S3对象键
 * @param {string} amzDate - AWS格式日期
 * @param {number} expiresIn - 过期时间
 * @param {string} signature - 签名
 * @returns {string} 完整的预签名URL
 */
function buildPresignedUrl(method, objectKey, amzDate, expiresIn, signature) {
  const host = getHostFromEndpoint(R2_CONFIG.endpoint);
  const encodedObjectKey = encodeURIComponent(objectKey).replace(/%2F/g, '/');

  const queryParams = {
    'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
    'X-Amz-Credential': `${R2_CONFIG.accessKeyId}/${amzDate.substring(0, 8)}/${R2_CONFIG.region}/s3/aws4_request`,
    'X-Amz-Date': amzDate,
    'X-Amz-Expires': expiresIn.toString(),
    'X-Amz-SignedHeaders': 'host',
    'X-Amz-Signature': signature
  };

  const queryString = Object.keys(queryParams)
    .sort()
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
    .join('&');

  return `https://${host}/${R2_CONFIG.bucketName}/${encodedObjectKey}?${queryString}`;
}

// ==================== 工具函数 ====================

/**
 * 从端点URL提取主机名
 * @param {string} endpoint - 端点URL
 * @returns {string} 主机名
 */
function getHostFromEndpoint(endpoint) {
  return endpoint.replace(/^https?:\/\//, '');
}

/**
 * 将字节数组转换为十六进制字符串
 * @param {Uint8Array} bytes - 字节数组
 * @returns {string} 十六进制字符串
 */
function bytesToHex(bytes) {
  return Array.from(bytes)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * 生成短UUID
 * @returns {string} 短UUID
 */
function generateShortUuid() {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
}

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名
 */
function getFileExtension(filename) {
  const parts = filename.split('.');
  return parts.length > 1 ? parts.pop().toLowerCase() : 'jpg';
}

console.log('AWS S3 Signature V4预签名URL生成服务已加载');
