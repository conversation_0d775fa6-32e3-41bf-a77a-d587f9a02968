// 自动化测试API - 用于测试认证和照片上传功能

// 创建测试用户并获取token的API
routerAdd("POST", "/api/test/create-test-user", (c) => {
  console.log('创建测试用户请求');
  
  try {
    const data = c.bind({});
    const { email, password } = data;
    
    if (!email || !password) {
      return c.json(400, {
        success: false,
        error: "需要email和password参数"
      });
    }
    
    // 尝试创建测试用户
    const usersCollection = $app.dao().findCollectionByNameOrId("users");
    const testUser = new Record(usersCollection);
    
    testUser.set("email", email);
    testUser.set("password", password);
    testUser.set("passwordConfirm", password);
    testUser.set("verified", true);
    
    try {
      $app.dao().saveRecord(testUser);
      console.log('测试用户创建成功:', testUser.id);
      
      return c.json(200, {
        success: true,
        message: "测试用户创建成功",
        userId: testUser.id,
        email: testUser.get("email")
      });
      
    } catch (createError) {
      console.error('创建用户失败:', createError);
      return c.json(400, {
        success: false,
        error: "用户可能已存在或创建失败: " + createError.message
      });
    }
    
  } catch (error) {
    console.error('创建测试用户异常:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

// 测试用户登录并获取token的API
routerAdd("POST", "/api/test/login", (c) => {
  console.log('测试用户登录请求');
  
  try {
    const data = c.bind({});
    const { email, password } = data;
    
    if (!email || !password) {
      return c.json(400, {
        success: false,
        error: "需要email和password参数"
      });
    }
    
    // 尝试认证用户
    try {
      const authResult = $app.dao().authWithPassword(
        $app.dao().findCollectionByNameOrId("users"),
        email,
        password
      );
      
      console.log('用户登录成功:', authResult.record.id);
      
      return c.json(200, {
        success: true,
        message: "登录成功",
        token: authResult.token,
        user: {
          id: authResult.record.id,
          email: authResult.record.get("email")
        }
      });
      
    } catch (authError) {
      console.error('用户认证失败:', authError);
      return c.json(401, {
        success: false,
        error: "认证失败: " + authError.message
      });
    }
    
  } catch (error) {
    console.error('登录测试异常:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

// 创建测试钓点的API
routerAdd("POST", "/api/test/create-test-spot", (c) => {
  console.log('创建测试钓点请求');
  
  try {
    const authRecord = c.get("authRecord");
    if (!authRecord) {
      return c.json(401, {
        success: false,
        error: "未授权访问"
      });
    }
    
    // 创建测试钓点
    const spotsCollection = $app.dao().findCollectionByNameOrId("fishing_spots");
    const testSpot = new Record(spotsCollection);
    
    testSpot.set("name", "测试钓点");
    testSpot.set("description", "这是一个测试钓点");
    testSpot.set("latitude", 31.2397);
    testSpot.set("longitude", 121.4999);
    testSpot.set("user_id", authRecord.id);
    testSpot.set("spot_type", "野钓");
    testSpot.set("fish_types", "淡水鱼");
    testSpot.set("is_public", true);
    testSpot.set("status", "active");
    
    try {
      $app.dao().saveRecord(testSpot);
      console.log('测试钓点创建成功:', testSpot.id);
      
      return c.json(200, {
        success: true,
        message: "测试钓点创建成功",
        spotId: testSpot.id,
        name: testSpot.get("name")
      });
      
    } catch (createError) {
      console.error('创建钓点失败:', createError);
      return c.json(400, {
        success: false,
        error: "钓点创建失败: " + createError.message
      });
    }
    
  } catch (error) {
    console.error('创建测试钓点异常:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

// 完整的测试流程API
routerAdd("GET", "/api/test/full-test", (c) => {
  return c.json(200, {
    success: true,
    message: "自动化测试API已加载",
    endpoints: [
      "POST /api/test/create-test-user - 创建测试用户",
      "POST /api/test/login - 用户登录获取token", 
      "POST /api/test/create-test-spot - 创建测试钓点",
      "GET /api/test/auth - 测试认证状态",
      "POST /api/photos/batch - 批量照片上传"
    ],
    instructions: [
      "1. 创建测试用户: POST /api/test/create-test-user {email, password}",
      "2. 登录获取token: POST /api/test/login {email, password}",
      "3. 使用token测试认证: GET /api/test/auth (带Authorization头)",
      "4. 创建测试钓点: POST /api/test/create-test-spot (带Authorization头)",
      "5. 测试照片上传: POST /api/photos/batch (带Authorization头)"
    ]
  });
});

console.log('自动化测试API已加载');
