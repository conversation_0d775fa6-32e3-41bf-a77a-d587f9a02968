// 自动创建必要的集合和字段
// 这个脚本会在PocketBase启动时检查并创建缺失的集合

// 创建spot_photos集合的API端点
routerAdd("POST", "/api/admin/create-spot-photos-collection", (c) => {
  console.log('收到创建spot_photos集合的请求');
  
  try {
    // 检查集合是否已存在
    try {
      const existingCollection = $app.dao().findCollectionByNameOrId("spot_photos");
      return c.json(200, {
        success: true,
        message: "spot_photos集合已存在",
        collectionId: existingCollection.id
      });
    } catch (e) {
      // 集合不存在，继续创建
    }
    
    // 创建集合
    const collection = new Collection();
    collection.name = "spot_photos";
    collection.type = "base";
    collection.system = false;
    
    // 定义字段
    const schema = [
      {
        name: "spot_id",
        type: "relation",
        required: true,
        options: {
          collectionId: "", // 需要在创建后设置
          cascadeDelete: true,
          minSelect: 1,
          maxSelect: 1,
          displayFields: ["name"]
        }
      },
      {
        name: "user_id", 
        type: "relation",
        required: true,
        options: {
          collectionId: "", // 需要在创建后设置
          cascadeDelete: false,
          minSelect: 1,
          maxSelect: 1,
          displayFields: ["email"]
        }
      },
      {
        name: "filename",
        type: "text",
        required: true,
        options: {
          min: 1,
          max: 255,
          pattern: ""
        }
      },
      {
        name: "url",
        type: "url",
        required: true,
        options: {
          exceptDomains: [],
          onlyDomains: []
        }
      },
      {
        name: "thumbnail_url",
        type: "url",
        required: false,
        options: {
          exceptDomains: [],
          onlyDomains: []
        }
      },
      {
        name: "storage_path",
        type: "text",
        required: false,
        options: {
          min: 0,
          max: 500,
          pattern: ""
        }
      },
      {
        name: "thumbnail_path",
        type: "text",
        required: false,
        options: {
          min: 0,
          max: 500,
          pattern: ""
        }
      },
      {
        name: "type",
        type: "select",
        required: true,
        options: {
          maxSelect: 1,
          values: ["normal", "panorama"]
        }
      },
      {
        name: "description",
        type: "text",
        required: false,
        options: {
          min: 0,
          max: 1000,
          pattern: ""
        }
      },
      {
        name: "sort_order",
        type: "number",
        required: true,
        options: {
          min: 0,
          max: null
        }
      },
      {
        name: "file_size",
        type: "number",
        required: false,
        options: {
          min: 0,
          max: null
        }
      },
      {
        name: "mime_type",
        type: "text",
        required: false,
        options: {
          min: 0,
          max: 100,
          pattern: ""
        }
      }
    ];
    
    collection.schema = schema;
    
    // 设置API规则
    collection.listRule = `@request.auth.id != "" && (spot_id.is_public = true || spot_id.user_id = @request.auth.id || user_id = @request.auth.id)`;
    collection.viewRule = `@request.auth.id != "" && (spot_id.is_public = true || spot_id.user_id = @request.auth.id || user_id = @request.auth.id)`;
    collection.createRule = `@request.auth.id != "" && @request.auth.id = user_id && spot_id.user_id = @request.auth.id`;
    collection.updateRule = `@request.auth.id != "" && @request.auth.id = user_id`;
    collection.deleteRule = `@request.auth.id != "" && @request.auth.id = user_id`;
    
    // 保存集合
    $app.dao().saveCollection(collection);
    
    // 更新关联字段的collectionId
    try {
      const fishingSpotsCollection = $app.dao().findCollectionByNameOrId("fishing_spots");
      const usersCollection = $app.dao().findCollectionByNameOrId("users");
      
      // 更新schema中的关联ID
      const updatedSchema = collection.schema.map(field => {
        if (field.name === "spot_id") {
          field.options.collectionId = fishingSpotsCollection.id;
        } else if (field.name === "user_id") {
          field.options.collectionId = usersCollection.id;
        }
        return field;
      });
      
      collection.schema = updatedSchema;
      $app.dao().saveCollection(collection);
      
    } catch (relationError) {
      console.log('关联集合设置警告:', relationError.message);
    }
    
    return c.json(200, {
      success: true,
      message: "spot_photos集合创建成功",
      collectionId: collection.id
    });
    
  } catch (error) {
    console.error('创建spot_photos集合失败:', error);
    return c.json(500, {
      success: false,
      error: error.message
    });
  }
});

console.log('集合创建脚本已加载');
