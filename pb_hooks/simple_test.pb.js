// 最简单的PocketBase测试
// 用于验证基本的API功能

// 最基本的GET测试
routerAdd("GET", "/api/simple/test", (e) => {
  console.log('收到简单测试请求');
  
  return e.json(200, {
    success: true,
    message: "简单测试成功",
    timestamp: new Date().toISOString(),
    auth: !!e.auth,
    authId: e.auth ? e.auth.id : null
  });
});

// 最基本的POST测试（不需要认证）
routerAdd("POST", "/api/simple/echo", (e) => {
  console.log('收到echo测试请求');
  
  try {
    // 尝试获取请求体
    const requestInfo = e.requestInfo();
    const body = requestInfo.body;
    
    console.log('请求体:', JSON.stringify(body));
    
    return e.json(200, {
      success: true,
      message: "Echo测试成功",
      receivedData: body,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Echo测试失败:', error);
    return e.json(500, {
      success: false,
      error: error.message || "Echo测试失败"
    });
  }
});

// 需要认证的POST测试
routerAdd("POST", "/api/simple/auth-echo", (e) => {
  console.log('收到认证echo测试请求');
  
  try {
    // 检查认证
    if (!e.auth) {
      return e.json(401, {
        success: false,
        error: "需要认证"
      });
    }
    
    // 获取请求体
    const requestInfo = e.requestInfo();
    const body = requestInfo.body;
    
    console.log('认证用户:', e.auth.id);
    console.log('请求体:', JSON.stringify(body));
    
    return e.json(200, {
      success: true,
      message: "认证Echo测试成功",
      userId: e.auth.id,
      userEmail: e.auth.get("email"),
      receivedData: body,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('认证Echo测试失败:', error);
    return e.json(500, {
      success: false,
      error: error.message || "认证Echo测试失败"
    });
  }
});

console.log('简单测试API已加载');
